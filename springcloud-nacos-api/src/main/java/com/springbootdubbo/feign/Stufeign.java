package com.springbootdubbo.feign;

import com.springbootdubbo.entry.Stu;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(
    value = "goods",
    path = "api/stu",
    fallback = StuFeignFallback.class  // 添加降级处理类
)
public interface Stufeign {

    @PostMapping
    public Stu submitStu(@PathVariable(value = "name") String name);
}
