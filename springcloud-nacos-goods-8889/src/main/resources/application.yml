server:
  port: 8889
spring:
  application:
    name: goods
  cloud:
    nacos:
      discovery:
        server-addr: *************:8848
    sentinel:
      transport:
        port: 8719  # 客户端端口
        dashboard: localhost:8080  # Sentinel控制台地址
      filter:
        enabled: true  # 启用Sentinel Web过滤器
      web-context-unify: false  # 保持URL路径作为资源名
      eager: true  # 应用启动时立即初始化Sentinel
      datasource:  # 规则数据源配置
        ds1:
          nacos:
            server-addr: *************:8848
            dataId: ${spring.application.name}-flow-rules
            groupId: SENTINEL_GROUP
            rule-type: flow