package com.springbootdubbo.services;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.springbootdubbo.entry.Stu;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class StuServicelmpl {

    @Value("8890")
    private Integer port;

    @SentinelResource(
        value = "submitStu-8890",  // 资源名称，区分不同端口的服务
        blockHandler = "submitStuBlockHandler"  // 限流处理方法
    )
    public Stu submitStu(String stuname) {
        return new Stu(stuname + "--" + port, 19, "男");
    }

    // 限流处理方法
    public Stu submitStuBlockHandler(String stuname, BlockException ex) {
        return new Stu("限流-" + stuname + "--" + port, 0, "限流");
    }
}
