package com.springbootdubbo.config;

import com.alibaba.csp.sentinel.slots.block.RuleConstant;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRule;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRuleManager;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

@Configuration
public class SentinelConfig {

    @PostConstruct
    public void initFlowRules() {
        List<FlowRule> rules = new ArrayList<>();
        
        // 创建学生接口限流规则
        FlowRule rule1 = new FlowRule();
        rule1.setResource("createStu");
        rule1.setGrade(RuleConstant.FLOW_GRADE_QPS);
        rule1.setCount(2); // QPS阈值为2
        rule1.setStrategy(RuleConstant.STRATEGY_DIRECT);
        rule1.setControlBehavior(RuleConstant.CONTROL_BEHAVIOR_DEFAULT);
        rules.add(rule1);

        // 商品服务8889限流规则
        FlowRule rule2 = new FlowRule();
        rule2.setResource("submitStu-8889");
        rule2.setGrade(RuleConstant.FLOW_GRADE_QPS);
        rule2.setCount(5); // QPS阈值为5
        rules.add(rule2);

        // 商品服务8890限流规则
        FlowRule rule3 = new FlowRule();
        rule3.setResource("submitStu-8890");
        rule3.setGrade(RuleConstant.FLOW_GRADE_QPS);
        rule3.setCount(5); // QPS阈值为5
        rules.add(rule3);

        // HTTP接口限流规则
        FlowRule rule4 = new FlowRule();
        rule4.setResource("POST:/stu");
        rule4.setGrade(RuleConstant.FLOW_GRADE_QPS);
        rule4.setCount(3); // QPS阈值为3
        rules.add(rule4);

        FlowRuleManager.loadRules(rules);
    }
}
