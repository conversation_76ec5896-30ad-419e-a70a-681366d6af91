package com.springbootdubbo.exception;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    @ExceptionHandler(Exception.class)
    public ResponseEntity<String> handleAllException(Exception e) {
        log.error("全局异常捕获:{},消息:{}", e.getClass().getName(), e.getMessage(), e);
        String message = e.getMessage();
        if (message != null) {
            switch (message) {
                case "系统繁忙,请稍后再试" -> {
                    return handleFlowControlException(e);
                }
                case "服务暂时不可用" -> {
                    return ResponseEntity.status(500).body("服务暂时不可用");
                }
            }
        }
    }

    private ResponseEntity<String> handleGenericException(Exception e) {
        log.error("未知异常", e);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("系统异常");
    }

    private ResponseEntity<String> handleFlowControlException(Exception e) {
    }
}
