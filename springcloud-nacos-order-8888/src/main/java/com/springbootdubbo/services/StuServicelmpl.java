package com.springbootdubbo.services;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.springbootdubbo.entry.Stu;
import com.springbootdubbo.feign.Stufeign;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class StuServicelmpl {

    @Resource
//    private RestTemplate restTemplate;
    private Stufeign stufeign;

    public void createStu(String name) {
//        Stu stu = restTemplate.postForObject("http://goods/api/stu", name, Stu.class);
//        log.info("创建学生:{}", stu);
        Stu stu = stufeign.submitStu(name);
        log.info("创建学生:{}", stu);
    }
}
