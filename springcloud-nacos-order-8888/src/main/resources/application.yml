server:
  port: 8888
spring:
  application:
    name: order
  cloud:
    nacos:
      discovery:
        server-addr: 192.168.163.1:8848
    #负载均衡 随机 低版本Nacos
    #goods:
    #  ribbon:
    #    NFLoadBalancerRuleClassName: com.netflix.loadbalancer.RandomRule

    #负载均衡 高版本 使用loadbalancer
    #    loadbalancer:
    #      ribbon:
    #        enabled: false
    sentinel:
      transport:
        dashboard: 127.0.0.1:8179